@extends('layouts.app')

@section('title', $service->title . ' - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@section('content')
<!-- Hero Section with <PERSON> Slider -->
<section class="hero-section position-relative {{ $banners->count() === 0 ? 'hero-fallback' : '' }}">
    @if($banners->count() > 0)
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                @foreach($banners as $index => $banner)
                <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                    <div class="banner-slide" style="background-image: url('{{ asset('storage/' . $banner->image_path) }}');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                @endforeach
            </div>

            @if($banners->count() > 1)
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                @foreach($banners as $index => $banner)
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="{{ $index }}"
                        class="{{ $index === 0 ? 'active' : '' }}" aria-current="true" aria-label="Slide {{ $index + 1 }}"></button>
                @endforeach
            </div>
            @endif
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb justify-content-center bg-transparent">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">หน้าแรก</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('services') }}" class="text-white">บริการ</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ $service->title }}</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-4 text-white">{{ $service->title }}</h1>
                    @if($service->category)
                    <div class="mb-3">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            @if($service->category->icon)
                            <i class="{{ $service->category->icon }} me-2"></i>
                            @endif
                            {{ $service->category->name }}
                        </span>
                    </div>
                    @endif
                    <p class="lead text-white">{{ $service->description }}</p>
                </div>
            </div>
        </div>
    @else
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb justify-content-center bg-transparent">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">หน้าแรก</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('services') }}">บริการ</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $service->title }}</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-4">{{ $service->title }}</h1>
                    @if($service->category)
                    <div class="mb-3">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            @if($service->category->icon)
                            <i class="{{ $service->category->icon }} me-2"></i>
                            @endif
                            {{ $service->category->name }}
                        </span>
                    </div>
                    @endif
                    <p class="lead">{{ $service->description }}</p>
                </div>
            </div>
        </div>
    @endif
</section>

<!-- Service Detail Section -->
<section class="py-5">
    <div class="container">
        <!-- Back Button -->
        <div class="mb-4">
            <button onclick="goBackTo('{{ route('services') }}')" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับ
            </button>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body p-4">
                        <!-- Main Image -->
                        <div class="mb-4">
                            @php
                                $mainImage = $service->images->where('is_cover', true)->first() ?? $service->images->first();
                                $mainImagePath = $mainImage ? $mainImage->image_path : $service->image;
                            @endphp
                            @if($mainImagePath)
                            <div class="img-container-fixed img-size-xlarge rounded shadow-sm">
                                <img src="{{ asset('storage/' . $mainImagePath) }}"
                                     class="img-fit-contain main-image"
                                     alt="{{ $service->title }}"
                                     style="cursor: pointer;"
                                     id="mainImage">
                            </div>
                            @else
                            <div class="bg-light d-flex align-items-center justify-content-center rounded shadow-sm" 
                                 style="height: 400px;">
                                <div class="text-center text-muted">
                                    <i class="fas fa-image fa-5x mb-3"></i>
                                    <p class="mb-0">ไม่มีรูปภาพ</p>
                                </div>
                            </div>
                            @endif
                        </div>



                        <!-- Image Gallery -->
                        @if($service->images->count() > 0)
                        <div class="mb-4">
                            <h5 class="mb-3">แกลเลอรี่รูปภาพ</h5>
                            <div class="row g-2">
                                @foreach($service->images->sortBy('sort_order') as $image)
                                <div class="col-md-3 col-4">
                                    <div class="gallery-image-container img-size-thumbnail">
                                        <img src="{{ asset('storage/' . $image->image_path) }}"
                                             class="img-fit-contain gallery-thumbnail"
                                             alt="{{ $image->description ?? $service->title }}"
                                             style="cursor: pointer;"
                                             onclick="openImageModal({{ $loop->index }})"
                                             data-image="{{ asset('storage/' . $image->image_path) }}"
                                             data-caption="{{ $image->description ?? $service->title }}">
                                        @if($image->is_cover)
                                        <div class="position-absolute top-0 end-0 m-1">
                                            <span class="badge bg-primary">รูปหลัก</span>
                                        </div>
                                        @endif
                                        <div class="position-absolute bottom-0 start-0 w-100 bg-dark bg-opacity-75 text-white p-1 gallery-thumb-caption" style="font-size: 0.75rem;">
                                            {{ Str::limit($image->description ?? 'รูปที่ ' . $loop->iteration, 20) }}
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Content -->
                        <div class="content">
                            <h3 class="mb-3">รายละเอียด</h3>
                            @if($service->details)
                                <div class="mb-4">
                                    {!! nl2br(e($service->details)) !!}
                                </div>
                            @else
                                <p class="text-muted mb-4">{{ $service->description }}</p>
                            @endif

                            <!-- Service Info -->
                            @if($service->category)
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        @if($service->category->icon)
                                        <i class="{{ $service->category->icon }} text-primary me-3 fa-lg"></i>
                                        @else
                                        <i class="fas fa-tags text-primary me-3 fa-lg"></i>
                                        @endif
                                        <div>
                                            <small class="text-muted d-block">หมวดหมู่</small>
                                            <strong>{{ $service->category->name }}</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <i class="fas fa-clock text-primary me-3 fa-lg"></i>
                                        <div>
                                            <small class="text-muted d-block">บริการ</small>
                                            <strong>ตลอด 24 ชั่วโมง</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif

                            <!-- Share Section -->
                            <div class="border-top pt-4">
                                <h5 class="mb-3">แชร์บริการนี้</h5>
                                <div class="d-flex gap-2">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}"
                                       target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-facebook me-1"></i>Facebook
                                    </a>
                                    <a href="https://line.me/R/msg/text/?{{ urlencode($service->title . ' - ' . request()->fullUrl()) }}"
                                       target="_blank" class="btn btn-outline-success btn-sm">
                                        <i class="fab fa-line me-1"></i>Line
                                    </a>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard()">
                                        <i class="fas fa-copy me-1"></i>คัดลอกลิงก์
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Contact CTA -->
                        <div class="bg-light p-4 rounded">
                            <div class="text-center">
                                <h4 class="fw-bold mb-3">สนใจบริการนี้?</h4>
                                <p class="mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก</p>
                                <div class="d-flex justify-content-center gap-3 flex-wrap">
                                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                                    </a>
                                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                                        <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                                    </a>
                                </div>
                                <small class="text-muted d-block mt-3">
                                    <i class="fas fa-clock me-1"></i>
                                    บริการตลอด 24 ชั่วโมง
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Service Category -->
                @if($service->category)
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">หมวดหมู่บริการ</h5>
                        <div class="d-flex align-items-center">
                            @if($service->category->icon)
                            <i class="{{ $service->category->icon }} fa-2x me-3" style="color: {{ $service->category->color }}"></i>
                            @endif
                            <div>
                                <h6 class="mb-1">{{ $service->category->name }}</h6>
                                @if($service->category->description)
                                <small class="text-muted">{{ $service->category->description }}</small>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Related Services -->
                @if($relatedServices->count() > 0)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">บริการที่เกี่ยวข้อง</h5>
                    </div>
                    <div class="card-body p-0">
                        @foreach($relatedServices as $relatedService)
                        <div class="border-bottom p-3">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    @php
                                        $relatedImage = $relatedService->images->where('is_cover', true)->first() ?? $relatedService->images->first();
                                        $relatedImagePath = $relatedImage ? $relatedImage->image_path : $relatedService->image;
                                    @endphp
                                    @if($relatedImagePath)
                                    <img src="{{ asset('storage/' . $relatedImagePath) }}" 
                                         class="rounded" 
                                         style="width: 60px; height: 60px; object-fit: cover;"
                                         alt="{{ $relatedService->title }}">
                                    @else
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    @endif
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="{{ route('services.show', $relatedService->id) }}" 
                                           class="text-decoration-none">{{ $relatedService->title }}</a>
                                    </h6>
                                    <p class="text-muted small mb-0">{{ Str::limit($relatedService->description, 80) }}</p>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Contact Card -->
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">ต้องการความช่วยเหลือ?</h5>
                        <p class="card-text">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก</p>
                        <div class="d-grid gap-2">
                            <a href="{{ route('contact') }}" class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                            </a>
                            <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                            </a>
                        </div>
                        <small class="text-muted d-block mt-2">
                            <i class="fas fa-clock me-1"></i>
                            บริการตลอด 24 ชั่วโมง
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="true" data-bs-keyboard="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $service->title }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="modalCloseBtn"></button>
            </div>
            <div class="modal-body text-center p-0 position-relative">
                @php
                    $modalMainImage = $service->images->where('is_cover', true)->first() ?? $service->images->first();
                    $modalMainImagePath = $modalMainImage ? $modalMainImage->image_path : $service->image;
                @endphp
                <img src="{{ asset('storage/' . $modalMainImagePath) }}"
                     class="img-fluid"
                     alt="{{ $service->title }}"
                     id="modalImage">

                <!-- Navigation arrows -->
                @if($service->images->count() > 1)
                <button class="btn btn-dark btn-sm position-absolute top-50 start-0 translate-middle-y ms-3"
                        id="prevImageBtn" style="z-index: 10;">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="btn btn-dark btn-sm position-absolute top-50 end-0 translate-middle-y me-3"
                        id="nextImageBtn" style="z-index: 10;">
                    <i class="fas fa-chevron-right"></i>
                </button>
                @endif
            </div>
            @if($service->images->count() > 1)
            <div class="modal-footer justify-content-center">
                <div class="d-flex gap-2 flex-wrap" id="modalThumbnails">
                    @foreach($service->images->sortBy('sort_order') as $index => $image)
                    <div class="modal-thumb {{ $index === 0 ? 'active' : '' }}"
                         style="width: 60px; height: 60px; cursor: pointer; border: 2px solid transparent; border-radius: 4px; overflow: hidden;"
                         data-index="{{ $index }}"
                         data-image="{{ asset('storage/' . $image->image_path) }}"
                         data-alt="{{ $image->description ?? $service->title }}">
                        <img src="{{ asset('storage/' . $image->image_path) }}"
                             class="img-thumbnail w-100 h-100"
                             style="object-fit: cover;"
                             alt="{{ $image->description ?? $service->title }}">
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Stable Modal Gallery Script -->
<script src="{{ asset('js/stable-modal.js') }}"></script>
<script>
// Service Image Gallery Management System (same as Activity)
class ServiceImageGallery {
    constructor() {
        this.currentImageIndex = 0;
        this.images = [];
        this.init();
    }

    init() {
        this.loadImages();
        this.setupEventListeners();
        this.setInitialActiveStates();
    }

    loadImages() {
        // Load all gallery images
        const galleryThumbnails = document.querySelectorAll('.gallery-thumbnail');
        galleryThumbnails.forEach((thumb, index) => {
            this.images.push({
                src: thumb.dataset.image,
                caption: thumb.dataset.caption || thumb.alt,
                element: thumb
            });
        });
    }

    setupEventListeners() {
        // Main image click to open modal
        const mainImage = document.getElementById('mainImage');
        if (mainImage) {
            mainImage.addEventListener('click', () => {
                this.openModal();
            });
        }

        // Gallery thumbnail clicks
        document.querySelectorAll('.gallery-thumbnail').forEach((thumb, index) => {
            thumb.addEventListener('click', (e) => {
                e.preventDefault();
                this.changeMainImage(index);
            });
        });

        // Modal thumbnail clicks
        document.querySelectorAll('.modal-thumb').forEach((thumb, index) => {
            thumb.addEventListener('click', (e) => {
                e.preventDefault();
                this.changeModalImage(index);
            });
        });

        // Modal navigation buttons
        const prevBtn = document.getElementById('prevImageBtn');
        const nextBtn = document.getElementById('nextImageBtn');

        if (prevBtn) {
            prevBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.previousImage();
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.nextImage();
            });
        }
    }

    setInitialActiveStates() {
        this.updateActiveThumbnails(0);
    }

    changeMainImage(index) {
        if (index < 0 || index >= this.images.length) return;

        this.currentImageIndex = index;
        const image = this.images[index];

        // Update main image with loading state
        const mainImage = document.getElementById('mainImage');
        if (mainImage) {
            mainImage.style.opacity = '0.5';

            // Preload image
            const newImg = new Image();
            newImg.onload = () => {
                mainImage.src = image.src;
                mainImage.alt = image.caption;
                mainImage.style.opacity = '1';
            };
            newImg.onerror = () => {
                console.error('Failed to load image:', image.src);
                mainImage.style.opacity = '1';
                this.showError('ไม่สามารถโหลดรูปภาพได้');
            };
            newImg.src = image.src;
        }

        // Update active thumbnails
        this.updateActiveThumbnails(index);
    }

    changeModalImage(index) {
        if (index < 0 || index >= this.images.length) return;

        this.currentImageIndex = index;
        const image = this.images[index];

        // Update modal image
        this.updateModalImage(image);

        // Update active modal thumbnails
        this.updateActiveModalThumbnails(index);
    }

    updateModalImage(image) {
        const modalImage = document.getElementById('modalImage');
        if (modalImage) {
            modalImage.style.opacity = '0.5';

            const newImg = new Image();
            newImg.onload = () => {
                modalImage.src = image.src;
                modalImage.alt = image.caption;
                modalImage.style.opacity = '1';

                // ปรับขนาดรูปภาพตามอัตราส่วน
                this.adjustImageSize(modalImage, newImg);
            };
            newImg.onerror = () => {
                console.error('Failed to load modal image:', image.src);
                modalImage.style.opacity = '1';
            };
            newImg.src = image.src;
        }
    }

    // ปรับขนาดรูปภาพตามอัตราส่วน
    adjustImageSize(modalImage, loadedImage) {
        if (!modalImage || !loadedImage) return;

        const imageWidth = loadedImage.naturalWidth || loadedImage.width;
        const imageHeight = loadedImage.naturalHeight || loadedImage.height;

        if (imageWidth === 0 || imageHeight === 0) return;

        // คำนวณอัตราส่วน
        const aspectRatio = imageWidth / imageHeight;

        // ลบ class เก่าทั้งหมด
        modalImage.classList.remove(
            'modal-image-landscape',
            'modal-image-portrait',
            'modal-image-square',
            'modal-image-wide',
            'modal-image-tall'
        );

        // กำหนด class ใหม่ตามอัตราส่วน
        if (aspectRatio > 2.5) {
            modalImage.classList.add('modal-image-wide');
        } else if (aspectRatio > 1.3) {
            modalImage.classList.add('modal-image-landscape');
        } else if (aspectRatio > 0.8) {
            modalImage.classList.add('modal-image-square');
        } else if (aspectRatio > 0.4) {
            modalImage.classList.add('modal-image-portrait');
        } else {
            modalImage.classList.add('modal-image-tall');
        }

        // ปรับขนาดเพิ่มเติมสำหรับรูปภาพขนาดใหญ่มาก
        if (imageWidth > 3000 || imageHeight > 3000) {
            modalImage.style.maxWidth = '80vw';
            modalImage.style.maxHeight = '70vh';
        } else if (imageWidth > 2000 || imageHeight > 2000) {
            modalImage.style.maxWidth = '85vw';
            modalImage.style.maxHeight = '75vh';
        }

        // ปรับสำหรับหน้าจอมือถือ
        if (window.innerWidth <= 768) {
            if (aspectRatio > 2) {
                modalImage.style.maxWidth = '95vw';
                modalImage.style.maxHeight = '45vh';
            } else if (aspectRatio < 0.6) {
                modalImage.style.maxWidth = '70vw';
                modalImage.style.maxHeight = '70vh';
            }
        }
    }

    updateActiveThumbnails(activeIndex) {
        // Remove active class from all thumbnails
        document.querySelectorAll('.gallery-thumbnail').forEach((thumb, index) => {
            const container = thumb.parentElement;
            if (index === activeIndex) {
                container.classList.add('active-thumbnail');
            } else {
                container.classList.remove('active-thumbnail');
            }
        });
    }

    updateActiveModalThumbnails(activeIndex) {
        // Update modal thumbnails
        document.querySelectorAll('.modal-thumb').forEach((thumb, index) => {
            if (index === activeIndex) {
                thumb.classList.add('active');
                thumb.style.borderColor = '#0d6efd';
            } else {
                thumb.classList.remove('active');
                thumb.style.borderColor = 'transparent';
            }
        });
    }

    nextImage() {
        const nextIndex = (this.currentImageIndex + 1) % this.images.length;
        this.changeModalImage(nextIndex);
    }

    previousImage() {
        const prevIndex = (this.currentImageIndex - 1 + this.images.length) % this.images.length;
        this.changeModalImage(prevIndex);
    }

    openModal() {
        const modalElement = document.getElementById('imageModal');
        if (modalElement) {
            // Clean up any existing modal instances
            const existingModal = bootstrap.Modal.getInstance(modalElement);
            if (existingModal) {
                existingModal.dispose();
            }

            // Create new modal instance
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: true,
                keyboard: true,
                focus: true
            });

            modal.show();

            // Add event listeners for proper cleanup
            modalElement.addEventListener('hidden.bs.modal', this.handleModalHidden.bind(this), { once: true });
        }
    }

    handleModalHidden() {
        // Clean up modal instances
        const modalElement = document.getElementById('imageModal');
        const modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.dispose();
        }

        // Remove any remaining backdrops
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });

        // Ensure body classes are cleaned up
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }

    showError(message) {
        console.error(message);
        // You can add a toast notification here if needed
    }
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if there are gallery images
    if (document.querySelectorAll('.gallery-thumbnail').length > 0) {
        window.serviceImageGallery = new ServiceImageGallery();
    }
});

// Global functions for backward compatibility
window.openImageModal = function(index = 0) {
    if (window.serviceImageGallery) {
        window.serviceImageGallery.changeModalImage(index);
        window.serviceImageGallery.openModal();
    }
};

window.changeMainImage = function(imageSrc, caption) {
    if (window.serviceImageGallery) {
        const index = window.serviceImageGallery.images.findIndex(img => img.src === imageSrc);
        if (index !== -1) {
            window.serviceImageGallery.changeMainImage(index);
        }
    }
};

window.changeModalImage = function(imageSrc, caption) {
    if (window.serviceImageGallery) {
        const index = window.serviceImageGallery.images.findIndex(img => img.src === imageSrc);
        if (index !== -1) {
            window.serviceImageGallery.changeModalImage(index);
        }
    }
};

function copyToClipboard() {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(function() {
        // Show success message
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>คัดลอกแล้ว';
        btn.classList.remove('btn-outline-secondary');
        btn.classList.add('btn-success');

        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}


</script>
@endsection
